"""
Создание тестовых данных для планировщика домашних заданий
"""
from datetime import datetime, timedelta
from database.repositories.user_repository import UserRepository
from database.repositories.student_repository import StudentRepository
from database.repositories.curator_repository import CuratorRepository
from database.repositories.homework_result_repository import HomeworkResultRepository
from database.repositories.homework_repository import HomeworkRepository
from database.repositories.subject_repository import SubjectRepository
from database.repositories.group_repository import GroupRepository


async def add_student_to_group_m2m(student_id: int, group_id: int):
    """Добавить студента в группу через M2M связь"""
    from database.database import get_db_session
    from database.models import student_groups
    from sqlalchemy import insert

    async with get_db_session() as session:
        # Проверяем, нет ли уже такой связи
        existing = await session.execute(
            student_groups.select().where(
                (student_groups.c.student_id == student_id) &
                (student_groups.c.group_id == group_id)
            )
        )

        if not existing.fetchone():
            # Добавляем связь
            await session.execute(
                insert(student_groups).values(
                    student_id=student_id,
                    group_id=group_id
                )
            )
            await session.commit()


async def create_scheduler_test_data():
    """Создать тестовые данные для проверки планировщика"""
    print("🕐 Создание тестовых данных для планировщика...")
    
    try:
        # 1. Создаем тестового куратора с Telegram ID
        print("   👨‍🏫 Создание тестового куратора...")
        
        curator_user = await UserRepository.create(
            telegram_id=777777001,  # Тестовый Telegram ID
            name="Куратор Тестовый",
            role="curator"
        )
        print(f"      ✅ Создан пользователь-куратор: {curator_user.name} (ID: {curator_user.telegram_id})")
        
        # Получаем первый предмет для привязки куратора
        subjects = await SubjectRepository.get_all()
        if subjects:
            subject = subjects[0]  # Берем первый предмет (например, Математика)
            
            curator = await CuratorRepository.create(
                user_id=curator_user.id,
                subject_id=subject.id
            )
            print(f"      ✅ Создан профиль куратора для предмета: {subject.name}")
            
            # Привязываем куратора к группам этого предмета
            groups = await GroupRepository.get_by_subject(subject.id)
            for group in groups[:2]:  # Привязываем к первым 2 группам
                await CuratorRepository.add_curator_to_group(curator.id, group.id)
                print(f"      ✅ Куратор добавлен в группу: {group.name}")
        
        # 2. Создаем неактивных студентов
        print("   🎓 Создание неактивных студентов...")
        
        inactive_students_data = [
            {
                'telegram_id': 777777002,
                'name': 'Неактивный Студент 1',
                'days_ago': 6  # 6 дней назад
            },
            {
                'telegram_id': 777777003,
                'name': 'Неактивный Студент 2', 
                'days_ago': 8  # 8 дней назад
            },
            {
                'telegram_id': 777777004,
                'name': 'Неактивный Студент 3',
                'days_ago': 10  # 10 дней назад
            }
        ]
        
        for student_data in inactive_students_data:
            # Создаем пользователя
            student_user = await UserRepository.create(
                telegram_id=student_data['telegram_id'],
                name=student_data['name'],
                role="student"
            )
            
            # Создаем профиль студента
            student = await StudentRepository.create(
                user_id=student_user.id,
                tariff="📦 Стандарт"
            )
            
            print(f"      ✅ Создан студент: {student_user.name} (ID: {student_user.telegram_id})")
            
            # Добавляем студента в группы
            if subjects:
                subject = subjects[0]
                groups = await GroupRepository.get_by_subject(subject.id)
                if groups:
                    group = groups[0]  # Добавляем в первую группу
                    await add_student_to_group_m2m(student.id, group.id)
                    print(f"      ✅ Студент добавлен в группу: {group.name}")
                    
                    # Создаем старый результат ДЗ
                    await create_old_homework_result(student, subject, student_data['days_ago'])
        
        # 3. Создаем активного студента для сравнения
        print("   ✅ Создание активного студента...")
        
        active_student_user = await UserRepository.create(
            telegram_id=777777005,
            name="Активный Студент",
            role="student"
        )
        
        active_student = await StudentRepository.create(
            user_id=active_student_user.id,
            tariff="⭐ Премиум"
        )
        
        print(f"      ✅ Создан активный студент: {active_student_user.name}")
        
        # Добавляем в группу и создаем недавний результат
        if subjects:
            subject = subjects[0]
            groups = await GroupRepository.get_by_subject(subject.id)
            if groups:
                group = groups[0]
                await add_student_to_group_m2m(active_student.id, group.id)
                await create_old_homework_result(active_student, subject, 1)  # 1 день назад
                print(f"      ✅ Активный студент добавлен в группу с недавним ДЗ")
        
        print("   ✅ Тестовые данные для планировщика созданы!")
        
        # Выводим итоговую информацию
        print("\n📊 СОЗДАННЫЕ ТЕСТОВЫЕ ДАННЫЕ:")
        print("="*50)
        print(f"👨‍🏫 Куратор: {curator_user.name} (Telegram: {curator_user.telegram_id})")
        print("🎓 Неактивные студенты:")
        for student_data in inactive_students_data:
            print(f"   • {student_data['name']} (Telegram: {student_data['telegram_id']}) - {student_data['days_ago']} дней")
        print(f"✅ Активный студент: {active_student_user.name} (Telegram: {active_student_user.telegram_id})")
        print("="*50)
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка создания тестовых данных планировщика: {e}")
        import traceback
        traceback.print_exc()
        return False


async def create_old_homework_result(student, subject, days_ago):
    """Создать старый результат ДЗ для студента"""
    try:
        # Получаем ДЗ по предмету
        homeworks = await HomeworkRepository.get_by_subject(subject.id)
        if not homeworks:
            print(f"      ⚠️ Нет ДЗ для предмета {subject.name}")
            return
        
        homework = homeworks[0]  # Берем первое ДЗ
        
        # Создаем результат
        result = await HomeworkResultRepository.create(
            student_id=student.id,
            homework_id=homework.id,
            total_questions=5,
            correct_answers=3,
            points_earned=9
        )
        
        # Обновляем дату на указанное количество дней назад
        old_date = datetime.now() - timedelta(days=days_ago)
        
        from database.database import get_db_session
        from sqlalchemy import update
        from database.models import HomeworkResult
        
        async with get_db_session() as session:
            await session.execute(
                update(HomeworkResult)
                .where(HomeworkResult.id == result.id)
                .values(completed_at=old_date, created_at=old_date)
            )
            await session.commit()
        
        print(f"      ✅ Создан результат ДЗ {days_ago} дней назад для {student.user.name}")
        
    except Exception as e:
        print(f"      ❌ Ошибка создания результата ДЗ: {e}")


async def create_test_notifications():
    """Создать тестовые уведомления для демонстрации"""
    print("   📨 Создание тестовых уведомлений...")
    
    try:
        from database.repositories.homework_notification_repository import HomeworkNotificationRepository
        
        # Получаем тестовых кураторов и студентов
        curators = await CuratorRepository.get_all()
        students = await StudentRepository.get_all()
        subjects = await SubjectRepository.get_all()
        
        if not (curators and students and subjects):
            print("      ⚠️ Недостаточно данных для создания уведомлений")
            return
        
        # Создаем несколько тестовых уведомлений
        test_notifications = [
            (curators[0].id, students[0].id, subjects[0].id),
            (curators[0].id, students[1].id, subjects[0].id) if len(students) > 1 else None,
        ]
        
        created_count = 0
        for notification_data in test_notifications:
            if notification_data:
                curator_id, student_id, subject_id = notification_data
                
                notification = await HomeworkNotificationRepository.create(
                    curator_id, student_id, subject_id
                )
                created_count += 1
                print(f"      ✅ Создано уведомление ID: {notification.id}")
        
        print(f"   ✅ Создано {created_count} тестовых уведомлений")
        
    except Exception as e:
        print(f"❌ Ошибка создания тестовых уведомлений: {e}")


async def show_scheduler_test_summary():
    """Показать сводку по тестовым данным планировщика"""
    print("\n📋 СВОДКА ТЕСТОВЫХ ДАННЫХ ПЛАНИРОВЩИКА:")
    print("="*60)
    
    try:
        # Кураторы с Telegram ID
        curators = await CuratorRepository.get_all()
        curator_count = 0
        for curator in curators:
            if curator.user.telegram_id >= 777777000:  # Тестовые ID
                curator_count += 1
                print(f"👨‍🏫 {curator.user.name} (Telegram: {curator.user.telegram_id})")
        
        # Студенты с Telegram ID
        students = await StudentRepository.get_all()
        student_count = 0
        for student in students:
            if student.user.telegram_id >= 777777000:  # Тестовые ID
                student_count += 1
                
                # Проверяем последнее ДЗ
                subjects = await SubjectRepository.get_all()
                if subjects:
                    last_date = await HomeworkResultRepository.get_last_homework_date_by_subject(
                        student.id, subjects[0].id
                    )
                    days_ago = (datetime.now() - last_date).days if last_date else "никогда"
                    print(f"🎓 {student.user.name} (Telegram: {student.user.telegram_id}) - последнее ДЗ: {days_ago}")
        
        # Активные уведомления
        from database.repositories.homework_notification_repository import HomeworkNotificationRepository
        active_notifications = await HomeworkNotificationRepository.get_active_notifications()
        
        print(f"\n📊 Статистика:")
        print(f"   • Тестовых кураторов: {curator_count}")
        print(f"   • Тестовых студентов: {student_count}")
        print(f"   • Активных уведомлений: {len(active_notifications)}")
        
        print("\n💡 Для тестирования планировщика:")
        print("   1. Запустите бота")
        print("   2. Используйте команду /scheduler_status (для админов)")
        print("   3. Или запустите scripts/test_homework_scheduler.py")
        
    except Exception as e:
        print(f"❌ Ошибка получения сводки: {e}")
    
    print("="*60)
