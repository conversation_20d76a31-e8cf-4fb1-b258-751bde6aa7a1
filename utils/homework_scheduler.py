"""
Планировщик уведомлений о домашних заданиях
"""
import logging
import asyncio
from datetime import datetime, time
from typing import List, Dict, Optional
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger
from aiogram import Bot

from database.repositories.homework_result_repository import HomeworkResultRepository
from database.repositories.homework_notification_repository import HomeworkNotificationRepository
from database.repositories.curator_repository import CuratorRepository


class HomeworkScheduler:
    """Планировщик для отправки уведомлений о невыполненных домашних заданиях"""
    
    def __init__(self, bot: Bot):
        self.bot = bot
        self.scheduler = AsyncIOScheduler()
        self.logger = logging.getLogger(__name__)
        
    async def start(self):
        """Запустить планировщик"""
        try:
            # Проверка каждый час на новых неактивных студентов
            self.scheduler.add_job(
                self.check_inactive_students,
                trigger=IntervalTrigger(hours=1),
                id='check_inactive_students',
                name='Проверка неактивных студентов каждый час',
                replace_existing=True
            )
            
            # Ежедневные напоминания в 8:00
            self.scheduler.add_job(
                self.send_daily_reminders,
                trigger=CronTrigger(hour=8, minute=0),
                id='daily_reminders',
                name='Ежедневные напоминания в 8:00',
                replace_existing=True
            )
            
            self.scheduler.start()
            self.logger.info("🕐 Планировщик домашних заданий запущен")
            self.logger.info("📅 Проверка каждый час + ежедневные напоминания в 8:00")
            
        except Exception as e:
            self.logger.error(f"❌ Ошибка запуска планировщика: {e}")
            raise
    
    async def stop(self):
        """Остановить планировщик"""
        try:
            if self.scheduler.running:
                self.scheduler.shutdown(wait=True)
                self.logger.info("🛑 Планировщик домашних заданий остановлен")
        except Exception as e:
            self.logger.error(f"❌ Ошибка остановки планировщика: {e}")
    
    async def check_inactive_students(self):
        """Проверить неактивных студентов и отправить первые уведомления"""
        try:
            self.logger.info("🔍 Начинаем проверку неактивных студентов...")
            
            # Получаем всех кураторов
            curators = await CuratorRepository.get_all()
            
            new_notifications_count = 0
            
            for curator in curators:
                if not curator.groups:
                    continue
                    
                # Получаем неактивных студентов для куратора
                inactive_students = await HomeworkResultRepository.get_inactive_students_for_curator(
                    curator.id, days=5
                )
                
                for student_data in inactive_students:
                    student = student_data['student']
                    subject = student_data['subject']
                    
                    # Проверяем, есть ли уже уведомление об этом студенте
                    notification_exists = await HomeworkNotificationRepository.get_notification_exists(
                        curator.id, student.id, subject.id
                    )
                    
                    if not notification_exists:
                        # Создаем новое уведомление
                        await HomeworkNotificationRepository.create(
                            curator.id, student.id, subject.id
                        )
                        
                        # Отправляем уведомление куратору
                        await self.send_notification_to_curator(curator, student_data)
                        new_notifications_count += 1
                        
                        # Небольшая задержка между отправками
                        await asyncio.sleep(0.5)
            
            if new_notifications_count > 0:
                self.logger.info(f"📨 Отправлено {new_notifications_count} новых уведомлений")
            else:
                self.logger.info("✅ Новых неактивных студентов не найдено")
                
        except Exception as e:
            self.logger.error(f"❌ Ошибка при проверке неактивных студентов: {e}")
    
    async def send_daily_reminders(self):
        """Отправить ежедневные напоминания (в 8:00)"""
        try:
            self.logger.info("📅 Отправляем ежедневные напоминания...")
            
            # Получаем уведомления для ежедневной отправки
            pending_notifications = await HomeworkNotificationRepository.get_pending_daily_notifications()
            
            sent_count = 0
            
            for notification in pending_notifications:
                curator = notification.curator
                student = notification.student
                subject = notification.subject
                
                # Формируем данные для отправки
                student_data = {
                    'student': student,
                    'subject': subject,
                    'last_homework_date': await HomeworkResultRepository.get_last_homework_date_by_subject(
                        student.id, subject.id
                    ),
                    'days_inactive': (datetime.now() - notification.first_notification_sent).days
                }
                
                # Отправляем напоминание
                await self.send_notification_to_curator(curator, student_data, is_reminder=True)
                
                # Обновляем время последнего уведомления
                await HomeworkNotificationRepository.update_last_notification_sent(notification.id)
                
                sent_count += 1
                await asyncio.sleep(0.5)
            
            if sent_count > 0:
                self.logger.info(f"📨 Отправлено {sent_count} ежедневных напоминаний")
            else:
                self.logger.info("✅ Нет уведомлений для ежедневной отправки")
                
        except Exception as e:
            self.logger.error(f"❌ Ошибка при отправке ежедневных напоминаний: {e}")
    
    async def send_notification_to_curator(self, curator, student_data: Dict, is_reminder: bool = False):
        """Отправить уведомление куратору"""
        try:
            student = student_data['student']
            subject = student_data['subject']
            last_homework_date = student_data.get('last_homework_date')
            days_inactive = student_data.get('days_inactive', 0)
            
            # Получаем невыполненные ДЗ
            incomplete_homeworks = await HomeworkResultRepository.get_incomplete_homeworks_for_student(
                student.id, subject.id
            )
            
            # Формируем сообщение
            message = await self.format_notification_message(
                student, subject, last_homework_date, days_inactive, 
                incomplete_homeworks, is_reminder
            )
            
            # Отправляем сообщение куратору
            telegram_id = curator.user.telegram_id
            if telegram_id:
                await self.bot.send_message(
                    chat_id=telegram_id,
                    text=message,
                    parse_mode='HTML'
                )
                
                self.logger.info(
                    f"📨 Уведомление отправлено куратору {curator.user.name} "
                    f"о студенте {student.user.name} (предмет: {subject.name})"
                )
            else:
                self.logger.warning(
                    f"⚠️ У куратора {curator.user.name} нет Telegram ID"
                )
                
        except Exception as e:
            self.logger.error(
                f"❌ Ошибка отправки уведомления куратору {curator.user.name}: {e}"
            )
    
    async def format_notification_message(
        self, 
        student, 
        subject, 
        last_homework_date: Optional[datetime], 
        days_inactive: int,
        incomplete_homeworks: List[Dict],
        is_reminder: bool = False
    ) -> str:
        """Форматировать сообщение уведомления"""
        
        # Заголовок
        if is_reminder:
            header = "🔔 <b>Ежедневное напоминание</b>"
        else:
            header = "🚨 <b>Уведомление о неактивном студенте</b>"
        
        # Информация о студенте и предмете
        student_info = f"👤 <b>Студент:</b> {student.user.name}\n"
        subject_info = f"📚 <b>Предмет:</b> {subject.name}\n"
        
        # Информация о неактивности
        if last_homework_date:
            last_date_str = last_homework_date.strftime("%d.%m.%Y")
            inactive_info = f"⏰ <b>Последнее ДЗ:</b> {last_date_str} ({days_inactive} дней назад)\n"
        else:
            inactive_info = f"⏰ <b>Домашние задания:</b> не выполнял ни одного\n"
        
        # Список невыполненных ДЗ
        homework_list = "\n<b>📝 Невыполненные домашние задания:</b>\n"
        
        if incomplete_homeworks:
            for hw_data in incomplete_homeworks[:10]:  # Показываем максимум 10 ДЗ
                homework = hw_data['homework']
                lesson = hw_data['lesson']
                homework_list += f"• <i>{lesson.name}:</i> {homework.name}\n"
            
            if len(incomplete_homeworks) > 10:
                homework_list += f"... и еще {len(incomplete_homeworks) - 10} заданий\n"
        else:
            homework_list += "• <i>Нет данных о невыполненных заданиях</i>\n"
        
        # Собираем полное сообщение
        message = f"{header}\n\n{student_info}{subject_info}{inactive_info}\n{homework_list}"
        
        return message

    async def resolve_notifications_for_student(self, student_id: int, subject_id: int):
        """Автоматически закрыть уведомления когда студент выполнил ДЗ"""
        try:
            resolved_count = await HomeworkNotificationRepository.mark_student_resolved_for_subject(
                student_id, subject_id
            )

            if resolved_count > 0:
                self.logger.info(
                    f"✅ Закрыто {resolved_count} уведомлений для студента ID:{student_id} "
                    f"по предмету ID:{subject_id}"
                )

        except Exception as e:
            self.logger.error(f"❌ Ошибка закрытия уведомлений: {e}")

    async def cleanup_old_notifications(self, days_old: int = 30):
        """Очистка старых решенных уведомлений"""
        try:
            deleted_count = await HomeworkNotificationRepository.delete_old_resolved_notifications(days_old)

            if deleted_count > 0:
                self.logger.info(f"🧹 Удалено {deleted_count} старых уведомлений")

        except Exception as e:
            self.logger.error(f"❌ Ошибка очистки уведомлений: {e}")

    async def get_scheduler_status(self) -> Dict:
        """Получить статус планировщика для мониторинга"""
        try:
            active_notifications = await HomeworkNotificationRepository.get_active_notifications()

            return {
                'running': self.scheduler.running if self.scheduler else False,
                'active_notifications_count': len(active_notifications),
                'jobs': [
                    {
                        'id': job.id,
                        'name': job.name,
                        'next_run': job.next_run_time.isoformat() if job.next_run_time else None
                    }
                    for job in self.scheduler.get_jobs()
                ] if self.scheduler else []
            }

        except Exception as e:
            self.logger.error(f"❌ Ошибка получения статуса планировщика: {e}")
            return {'error': str(e)}
